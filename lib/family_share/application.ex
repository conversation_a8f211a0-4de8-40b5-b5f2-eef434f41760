defmodule FamilyShare.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    children = [
      FamilyShareWeb.Telemetry,
      FamilyShare.Repo,
      {DNSCluster, query: Application.get_env(:family_share, :dns_cluster_query) || :ignore},
      {Phoenix.PubSub, name: FamilyShare.PubSub},
      # Start the Finch HTTP client for sending emails
      {<PERSON>, name: FamilyShare.Finch},
      # Start a worker by calling: FamilyShare.Worker.start_link(arg)
      # {FamilyShare.Worker, arg},
      # Start to serve requests, typically the last entry
      FamilyShareWeb.Endpoint
    ]

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: FamilyShare.Supervisor]
    Supervisor.start_link(children, opts)
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    FamilyShareWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
