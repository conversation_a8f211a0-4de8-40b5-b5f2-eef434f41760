<!DOCTYPE html>
<html lang="en" class="[scrollbar-gutter:stable]">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="csrf-token" content={get_csrf_token()} />
    <.live_title default="FamilyShare" suffix=" · Phoenix Framework">
      {assigns[:page_title]}
    </.live_title>
    <link phx-track-static rel="stylesheet" href={~p"/assets/app.css"} />
    <script defer phx-track-static type="text/javascript" src={~p"/assets/app.js"}>
    </script>

  </head>
  <body class="bg-white">
    {@inner_content}
    <script>
      function toggleMobileMenu() {
        const overlay = document.getElementById('mobile-menu-overlay');
        overlay.classList.toggle('hidden');
      }
    </script>
  </body>
</html>
